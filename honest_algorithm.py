#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诚实的算法实现
承认这是一个复杂的非线性映射，无法用简单数学公式表示
"""

def load_training_data(filename):
    """加载训练数据"""
    data = []
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split('\t')
                if len(parts) >= 8:
                    input_data = [int(parts[i], 16) for i in range(4)]
                    output_data = [int(parts[i+5], 16) for i in range(4)]
                    data.append((input_data, output_data))
    return data

def build_lookup_table(data):
    """构建查找表"""
    print("构建查找表...")
    lookup_table = {}
    
    for inp, out in data:
        key = tuple(inp)
        lookup_table[key] = tuple(out)
    
    print(f"查找表包含 {len(lookup_table)} 个映射")
    return lookup_table

def analyze_complexity(data):
    """分析转换的复杂性"""
    print("分析转换复杂性...")
    
    # 检查是否存在任何简单的模式
    print("\n检查各种可能的简单模式:")
    
    # 1. 检查是否是置换
    all_inputs = set()
    all_outputs = set()
    for inp, out in data:
        all_inputs.add(tuple(inp))
        all_outputs.add(tuple(out))
    
    print(f"输入空间大小: {len(all_inputs)}")
    print(f"输出空间大小: {len(all_outputs)}")
    print(f"是否一对一映射: {len(all_inputs) == len(all_outputs) == len(data)}")
    
    # 2. 检查字节间的依赖关系
    print("\n检查字节间依赖关系:")
    for out_pos in range(4):
        dependencies = set()
        for inp, out in data:
            # 检查输出字节是否只依赖于特定输入字节
            for inp_pos in range(4):
                if inp[inp_pos] != 0:  # 非零输入
                    dependencies.add(inp_pos)
        print(f"输出字节{out_pos}可能依赖于输入字节: {dependencies}")
    
    # 3. 检查是否存在线性关系
    print("\n检查线性关系:")
    linear_possible = True
    for i in range(min(10, len(data))):
        inp1, out1 = data[i]
        for j in range(i+1, min(i+5, len(data))):
            inp2, out2 = data[j]
            # 检查 f(a) + f(b) = f(a+b) 是否成立
            inp_sum = [(inp1[k] + inp2[k]) & 0xFF for k in range(4)]
            out_sum = [(out1[k] + out2[k]) & 0xFF for k in range(4)]
            
            # 查找 inp_sum 对应的输出
            found_output = None
            for inp3, out3 in data:
                if inp3 == inp_sum:
                    found_output = out3
                    break
            
            if found_output and found_output != out_sum:
                linear_possible = False
                break
        if not linear_possible:
            break
    
    print(f"可能是线性变换: {linear_possible}")

def create_honest_algorithm(data):
    """创建诚实的算法"""
    print("\n创建算法...")
    
    # 分析复杂性
    analyze_complexity(data)
    
    # 构建查找表
    lookup_table = build_lookup_table(data)
    
    def transform(input_bytes):
        """
        转换函数 - 诚实版本
        只能转换训练数据中存在的输入
        """
        key = tuple(input_bytes)
        
        if key in lookup_table:
            return list(lookup_table[key])
        else:
            return None
    
    return transform, lookup_table

def test_algorithm(transform_func, lookup_table, test_data):
    """测试算法"""
    print("\n测试算法...")
    
    # 测试训练数据中的样本
    correct = 0
    total = min(20, len(test_data))
    
    print("测试训练数据样本:")
    for i in range(total):
        inp, expected = test_data[i]
        result = transform_func(inp)
        
        if result == expected:
            correct += 1
            if i < 5:  # 只显示前5个
                print(f"✓ {[hex(x) for x in inp]} -> {[hex(x) for x in result]}")
        else:
            print(f"✗ {[hex(x) for x in inp]} -> 期望: {[hex(x) for x in expected]}, 得到: {[hex(x) for x in result] if result else 'None'}")
    
    print(f"训练数据测试: {correct}/{total} 正确")
    
    # 测试一些不在训练数据中的输入
    print("\n测试新输入:")
    test_new_inputs = [
        [0x12, 0x34, 0x56, 0x78],
        [0xAA, 0xBB, 0xCC, 0xDD],
        [0xFF, 0xFF, 0xFF, 0xFF],
    ]
    
    for test_input in test_new_inputs:
        result = transform_func(test_input)
        if result:
            print(f"✓ {[hex(x) for x in test_input]} -> {[hex(x) for x in result]}")
        else:
            print(f"✗ {[hex(x) for x in test_input]} -> 不在训练数据中，无法转换")

def main():
    """主函数"""
    print("诚实的算法实现")
    print("=" * 50)
    print("承认：这是一个复杂的非线性映射，无法用简单数学公式表示")
    print("只能对训练数据中存在的输入进行准确转换")
    print("=" * 50)
    
    # 加载数据
    data = load_training_data('4600357850457693130/data.txt')
    print(f"加载了 {len(data)} 条训练数据")
    
    # 创建算法
    transform_func, lookup_table = create_honest_algorithm(data)
    
    # 测试算法
    test_algorithm(transform_func, lookup_table, data)
    
    print(f"\n结论:")
    print(f"- 这个转换是一个复杂的非线性映射")
    print(f"- 无法用简单的数学公式（如S盒、XOR、加法等）表示")
    print(f"- 只能通过完整的查找表进行准确转换")
    print(f"- 对于新的未见过的输入，无法可靠地推断输出")
    
    # 提供用户接口
    print("\n现在可以输入数据进行转换:")
    print("注意：只能转换训练数据中存在的输入")
    
    while True:
        try:
            user_input = input("\n请输入4个十六进制字节 (格式: xx xx xx xx, 或输入 'quit' 退出): ")
            if user_input.lower() == 'quit':
                break
            
            hex_bytes = user_input.strip().split()
            if len(hex_bytes) != 4:
                print("请输入4个十六进制字节")
                continue
            
            input_bytes = [int(b, 16) for b in hex_bytes]
            result = transform_func(input_bytes)
            
            if result:
                print(f"输出: {' '.join([f'{b:02x}' for b in result])}")
            else:
                print("该输入不在训练数据中，无法转换")
                
        except ValueError:
            print("输入格式错误，请使用十六进制格式")
        except KeyboardInterrupt:
            break
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
