#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据转换算法
基于提供的数据文件实现输入输出转换
"""

def load_transformation_table(filename):
    """加载转换表"""
    transformation_table = {}
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split('\t')
                if len(parts) >= 8:
                    # 输入数据：前4列
                    input_bytes = [int(parts[i], 16) for i in range(4)]
                    # 输出数据：后4列
                    output_bytes = [int(parts[i+5], 16) for i in range(4)]
                    
                    # 将输入转换为32位整数作为键
                    input_key = (input_bytes[0] << 24) | (input_bytes[1] << 16) | (input_bytes[2] << 8) | input_bytes[3]
                    transformation_table[input_key] = output_bytes
    
    return transformation_table

def transform_data(input_bytes, transformation_table):
    """
    转换数据
    input_bytes: 4个字节的列表 [byte0, byte1, byte2, byte3]
    返回: 4个字节的列表 [out_byte0, out_byte1, out_byte2, out_byte3] 或 None
    """
    # 将输入转换为32位整数
    input_key = (input_bytes[0] << 24) | (input_bytes[1] << 16) | (input_bytes[2] << 8) | input_bytes[3]
    
    # 查找转换表
    if input_key in transformation_table:
        return transformation_table[input_key]
    else:
        return None

def main():
    """主函数"""
    print("数据转换算法")
    print("=" * 40)
    
    # 加载转换表
    try:
        transformation_table = load_transformation_table('4600357850457693130/data.txt')
        print(f"成功加载 {len(transformation_table)} 个转换规则")
    except FileNotFoundError:
        print("错误: 找不到数据文件 '4600357850457693130/data.txt'")
        return
    except Exception as e:
        print(f"错误: 加载数据文件时出错 - {e}")
        return
    
    # 测试几个已知的转换
    test_cases = [
        [0x00, 0x00, 0x00, 0x01],
        [0x00, 0x00, 0x00, 0x15],
        [0xab, 0x04, 0x4d, 0x26],
        [0x01, 0x01, 0x01, 0x01]
    ]
    
    print("\n测试已知转换:")
    for test_input in test_cases:
        result = transform_data(test_input, transformation_table)
        if result:
            print(f"输入: {' '.join([f'{b:02x}' for b in test_input])} -> 输出: {' '.join([f'{b:02x}' for b in result])}")
        else:
            print(f"输入: {' '.join([f'{b:02x}' for b in test_input])} -> 未找到转换规则")
    
    # 用户交互界面
    print("\n现在可以输入新的数据进行转换:")
    while True:
        try:
            user_input = input("\n请输入4个十六进制字节 (格式: xx xx xx xx, 或输入 'quit' 退出): ")
            if user_input.lower() == 'quit':
                break
            
            # 解析输入
            hex_bytes = user_input.strip().split()
            if len(hex_bytes) != 4:
                print("请输入4个十六进制字节")
                continue
            
            input_bytes = [int(b, 16) for b in hex_bytes]
            result = transform_data(input_bytes, transformation_table)
            
            if result:
                print(f"输出: {' '.join([f'{b:02x}' for b in result])}")
            else:
                print("未找到对应的转换规则")
                
        except ValueError:
            print("输入格式错误，请使用十六进制格式")
        except KeyboardInterrupt:
            break
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
