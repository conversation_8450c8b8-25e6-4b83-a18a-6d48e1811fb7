#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
bit_linear.py  ——  纯算法按位实现

★ 第一次：
        python bit_linear.py learn dump.txt      # 从样本里学 32 个列向量
★ 以后：
        from bit_linear import F
        y = F(0x12345678)
"""
import sys, re, math, pickle, pathlib
from typing import List

# ---------------------------------------------------------------------------
# 全局：32 个 32-bit 列向量。首次 learn 时会写入这个文件。
# ---------------------------------------------------------------------------
CACHE = pathlib.Path("cols32.bin")   # 32 × uint32 little-endian

# ---------------------------------------------------------------------------
# 按位线性变换本体
# ---------------------------------------------------------------------------
def F(x: int, cols: List[int] = None) -> int:
    """
    计算 32-bit 输入 x 的映射 F(x)。
    - cols : 长度 32 的 list[int]，下标按 bit 0..31 排列。
             若为空则自动尝试从缓存文件载入。
    """
    if cols is None:
        if not CACHE.exists():
            raise RuntimeError("尚未生成列向量，请先执行 `python bit_linear.py learn dump.txt`")
        cols = pickle.loads(CACHE.read_bytes())

    y = 0
    b = x
    bit = 0
    while b:
        if b & 1:
            y ^= cols[bit]
        bit += 1
        b >>= 1
    # 如果输入高位很多 0，上面循环很快；极端情况可换成 for-bit 写法
    return y

# ---------------------------------------------------------------------------
# 学习阶段：从文本文件里提取 32 列
# ---------------------------------------------------------------------------
def learn(file: pathlib.Path):
    """
    读取 dump，自动定位“单比特输入 → 输出”行，
    构造 32 个列向量并缓存。
    """
    raw = file.read_text(encoding="utf-8", errors="ignore")
    tok_re = re.compile(r"\b[0-9A-Fa-f]{2}\b")

    cols = [None]*32       # placeholder

    for line in raw.splitlines():
        parts = tok_re.findall(line)
        if len(parts) != 8:
            continue

        # ------------------ 解析输入 4 个字节 ------------------
        b3,b2,b1,b0 = [int(x,16) for x in parts[:4]]
        x = (b3<<24)|(b2<<16)|(b1<<8)|b0
        if x == 0 or (x & (x-1)):          # 不是单比特 → 跳过
            continue

        bit_index = int(math.log2(x))      # 0..31
        # ------------------ 解析输出 4 个字节（小端→host） -----
        o0,o1,o2,o3 = [int(x,16) for x in parts[4:]]
        y = (o3<<24)|(o2<<16)|(o1<<8)|o0   # host endian

        cols[bit_index] = y

    # 检查是否 32 列都找齐
    missing = [i for i,c in enumerate(cols) if c is None]
    if missing:
        raise ValueError(f"样本里缺少 {len(missing)} 个比特列: {missing}")

    CACHE.write_bytes(pickle.dumps(cols))
    print("✔ 已从样本学得 32 列并写入", CACHE)

# ---------------------------------------------------------------------------
# 命令行入口
# ---------------------------------------------------------------------------
def _main(argv):
    if len(argv) < 2:
        print(__doc__)
        return

    if argv[1] == "learn":
        if len(argv) != 3:
            print("用法: python bit_linear.py learn dump.txt")
            return
        learn(pathlib.Path(argv[2]))
        return

    # 直接计算
    try:
        x = int(argv[1], 16)
    except ValueError:
        print("请输入十六进制数字，如 0x89ABCDEF")
        return

    y = F(x)
    print(f"F({x:#010x}) = {y:#010x}")

# ---------------------------------------------------------------------------
if __name__ == "__main__":
    _main(sys.argv)
