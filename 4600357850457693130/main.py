#!/usr/bin/env python3
# -*- coding: utf-8 -*-
#
#  encode32.py – reproduce the 4-byte→4-byte变换
#
#  使用方法:
#      >>> encode(bytes.fromhex('00000001'))
#      b'\xc5\xd6R\xd9'

# ------------------------------------------------------------
#  低 4 bit 的非线性查找表 (0-F)      ——>   -8 … +6, +5, -?
#  栏目里的数字即要加到 base2 上（正负都有）
LOOKUP_LOW_NIBBLE = [
     0,  -1,  +2,  +1,
    +4,  +3,  +6,  +5,
    -8,  -9,  -6,  -7,
    -4,  -5,  -2,  -3,
]

def _byte0(d: int, a: int) -> int:
    """输出字节 0  ——依赖  input[3] (d) 与 input[0] (a)"""
    base = (0xC6 + 4*(d >> 2) - (d & 0x03)) & 0xFF
    return base ^ a                     # 0x04 → 异或, 0x01 → 异或…

def _byte1(c: int, b: int) -> int:
    """输出字节 1  ——依赖  input[2] (c) 与 input[1] (b)"""
    base = (0xD6 + (c & 0xF0) - (c & 0x0F)) & 0xFF
    return base ^ b                     # 同样做一次异或补偿

def _byte2(c: int, b: int) -> int:
    """输出字节 2  ——依赖  input[2] (c) 与 input[1] (b)"""
    lo  = c & 0x0F                      # 低 4 bit
    hi  = c & 0xF0                      # 高 4 bit
    base = (0x52 + hi + LOOKUP_LOW_NIBBLE[lo]) & 0xFF
    return base ^ b

def _byte3(d: int, a: int) -> int:
    """输出字节 3  ——依赖  input[3] (d) 与 input[0] (a)

       观察：d 的高 2 bit 决定‘加/减’方向
              ── 00xx →  +a
              ── 01xx →  -a
              ── 10xx →  -a
              ── 11xx →  +a        (# 07h 一类特殊值）
    """
    base = (0xD8 - 4*(d >> 2) + (d & 0x03)) & 0xFF
    group = (d >> 2) & 0x03            # 以 4 为步长分区
    if group in (0, 3):                # 0-3, 12-15 ……正向
        return (base + a) & 0xFF
    else:                              # 4-7, 8-11 ……反向
        return (base - a) & 0xFF

def encode(word: bytes) -> bytes:
    """
    把 4 字节输入映射成 4 字节输出
    参数 word 必须是 bytes/bytearray, len == 4
    """
    if len(word) != 4:
        raise ValueError('需要恰好 4 个字节')

    a, b, c, d = word               # 按表里顺序直接拆 4 字节
    o0 = _byte0(d, a)
    o1 = _byte1(c, b)
    o2 = _byte2(c, b)
    o3 = _byte3(d, a)
    return bytes((o0, o1, o2, o3))


# -------------------------- 自测 ----------------------------
if __name__ == '__main__':
    # 表里挑几组做 sanity-check
    TEST_VECTORS = {
        '14141414': 'eefe7af0',
        '00000001': 'c5d652d9',
        '00000004': 'cad652d4',
        '01010101': 'c4d450da',
        '02020202': 'c2d256dc',
        '04040404': 'cede5ad0',
        '08080808': 'd6c642c8',
    }
    for inp_hex, expect_hex in TEST_VECTORS.items():
        out_hex = encode(bytes.fromhex(inp_hex)).hex()
        assert out_hex == expect_hex, f'{inp_hex} → {out_hex} (应为 {expect_hex})'
    print('所有示例都通过 ✔︎')
