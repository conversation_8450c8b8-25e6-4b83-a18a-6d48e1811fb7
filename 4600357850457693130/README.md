# 加密算法解密器

## 概述

这是一个基于30组样本数据推导出的封闭映射加密算法的解密器。该算法将32位输入映射为4字节输出，主要用于解决特定的加密问题。

## 算法原理

### 核心思想
- 输入：32位无符号整数（实际只使用最低1字节）
- 输出：4字节密文
- 映射规律：通过分析30组样本数据推导出的数学规律

### 算法步骤

#### 1. 输入预处理
```python
x_byte = x & 0xFF  # 只取输入的最低1字节
```

#### 2. 计算第一字节 (Y0)
```python
i = (x_byte - 1) & 0xFF           # 索引计算
g, k = i >> 2, i & 0x03           # 组号g和组内偏移k
base = (0xC5 + (g << 2)) & 0xFF   # 基准值，每组递增0x04
y0 = (base + OFFSET_TABLE[k]) & 0xFF
```

**关键参数：**
- `OFFSET_TABLE = (0, -1, -2, +5)` - 组内四个元素的相对位移
- 基准值从0xC5开始，每组递增0x04

#### 3. 计算第四字节 (Y3)
```python
hi = x_byte >> 4                  # 输入的高4位
K = (0x9E + (hi << 5)) & 0xFF     # 保证同一高4位下Y0+Y3为常数
y3 = (K - y0) & 0xFF
```

#### 4. 固定字节
- 第二字节：固定为 `0xD6`
- 第三字节：固定为 `0x52`

#### 5. 组装结果
```python
return bytes((y0, 0xD6, 0x52, y3))
```

## 使用方法

### 命令行使用
```bash
python main.py <hex_value>
```

### 参数说明
- `<hex_value>`：32位十六进制数，可以带0x前缀或不带

### 使用示例

```bash
# 示例1：带0x前缀
python main.py 0x00000005
# 输出：c9d652d5

# 示例2：不带0x前缀
python main.py 1d
# 输出：e1d652dd

# 示例3：其他值
python main.py 0x00000010
python main.py ff
```

## 算法特点

### 1. 分组映射
- 将输入按4个一组进行分组处理
- 每组有不同的基准值和偏移量

### 2. 位运算优化
- 大量使用位运算提高计算效率
- 通过位掩码确保结果在有效范围内

### 3. 固定模式
- 中间两字节固定为0xD6和0x52
- 简化了算法复杂度

### 4. 数学约束
- 保证同一高4位输入下，Y0+Y3为常数
- 确保映射的一致性和可预测性

## 技术细节

### 组号计算
```
组号 g = (输入-1) >> 2
组内偏移 k = (输入-1) & 0x03
```

### 基准值计算
```
基准值 = (0xC5 + g*4) & 0xFF
```

### 约束关系
```
对于相同的高4位输入，Y0 + Y3 = 常数K
其中 K = (0x9E + 高4位*32) & 0xFF
```

## 文件结构

```
4600357850457693130/
├── main.py          # 主程序文件
└── README.md        # 本说明文档
```

## 注意事项

1. **输入范围**：虽然接受32位输入，但实际只使用最低1字节
2. **输出格式**：输出为8位十六进制字符串（小端序）
3. **错误处理**：程序会检查输入格式的合法性
4. **样本依赖**：算法基于30组样本推导，适用于特定的加密场景

## 扩展说明

这个算法是通过逆向工程从样本数据中推导出来的，具有以下特征：
- 确定性映射：相同输入总是产生相同输出
- 封闭性：算法完全自包含，不依赖外部数据
- 高效性：计算复杂度为O(1)

如果需要处理更大范围的输入或修改映射规律，可以调整相关常数和偏移表。
