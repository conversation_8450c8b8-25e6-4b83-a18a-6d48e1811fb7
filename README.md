# 数据转换算法

这是一个基于查找表的数据转换算法，能够根据提供的数据文件实现4字节输入到4字节输出的转换。

## 功能说明

该算法通过分析输入输出数据间的对应关系，建立了一个完整的转换查找表，可以准确地将4个字节的输入数据转换为对应的4个字节输出数据。

## 文件结构

- `data_transform.py` - 主要的转换算法实现
- `4600357850457693130/data.txt` - 包含1463条输入输出对应关系的数据文件
- `README.md` - 使用说明文档

## 使用方法

### 运行程序

```bash
python data_transform.py
```

### 输入格式

程序会提示您输入4个十六进制字节，格式为：`xx xx xx xx`

例如：
- `00 00 00 01`
- `ff ff ff ff`
- `ab 04 4d 26`

### 输出结果

如果输入的数据在转换表中存在，程序会输出对应的4个十六进制字节。
如果输入的数据不在转换表中，程序会提示"未找到对应的转换规则"。

## 示例

```
请输入4个十六进制字节 (格式: xx xx xx xx, 或输入 'quit' 退出): 00 00 00 01
输出: c5 d6 52 d9

请输入4个十六进制字节 (格式: xx xx xx xx, 或输入 'quit' 退出): ff ff ff ff
输出: 38 28 ac 26

请输入4个十六进制字节 (格式: xx xx xx xx, 或输入 'quit' 退出): 12 34 56 78
未找到对应的转换规则
```

## 算法特点

1. **基于查找表**: 算法使用预建的查找表进行转换，确保准确性
2. **完整覆盖**: 包含1463个已知的输入输出对应关系
3. **快速查找**: 使用32位整数作为键值，查找效率高
4. **用户友好**: 提供交互式界面，支持连续输入测试

## 数据规律分析

通过对数据的分析发现：
- 输入输出之间的关系是复杂的非线性映射
- 不是简单的字节级别一对一映射
- 需要将4个字节作为整体进行转换
- 转换规律类似于加密算法的S盒替换

## 注意事项

- 只能转换数据文件中已存在的输入值
- 输入必须是4个十六进制字节
- 程序区分大小写，建议使用小写十六进制
- 输入 'quit' 可以退出程序

## 技术实现

算法的核心实现：

1. **数据加载**: 从文本文件中读取输入输出对应关系
2. **键值映射**: 将4字节输入转换为32位整数作为查找键
3. **快速查找**: 使用Python字典实现O(1)时间复杂度的查找
4. **结果返回**: 返回对应的4字节输出数组

这种实现方式确保了转换的准确性和效率。
